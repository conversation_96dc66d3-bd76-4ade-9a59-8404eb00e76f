<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>icon/film</title>
    <g id="超级编导-素材库、个人设置、周报日报" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人设置-关于蚁小二" transform="translate(-228.000000, -146.000000)">
            <g id="编组-4" transform="translate(216.000000, 90.000000)">
                <g id="编组-3" transform="translate(0.000000, 44.000000)">
                    <g id="icon/film" transform="translate(12.000000, 12.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <path d="M8,1.5 C6.77222222,1.5 5.83333333,2.43888889 5.83333333,3.66666667 L5.83333333,5.83333333 M10.1666667,5.83333333 L10.1666667,3.66666667 C10.1666667,2.43888889 9.22777778,1.5 8,1.5 M10.8888889,14.5 L5.11111111,14.5 C3.88333333,14.5 2.94444444,13.5611111 2.94444444,12.3333333 L2.94444444,8 C2.94444444,6.77222222 3.88333333,5.83333333 5.11111111,5.83333333 L10.8888889,5.83333333 C12.1166667,5.83333333 13.0555556,6.77222222 13.0555556,8 L13.0555556,12.3333333 C13.0555556,13.5611111 12.1166667,14.5 10.8888889,14.5 Z M8,8.72222222 C7.20555556,8.72222222 6.55555556,9.37222222 6.55555556,10.1666667 C6.55555556,10.9611111 7.20555556,11.6111111 8,11.6111111 C8.79444444,11.6111111 9.44444444,10.9611111 9.44444444,10.1666667 C9.44444444,9.37222222 8.79444444,8.72222222 8,8.72222222 Z" id="形状" stroke="currentColor" stroke-width="1.33" stroke-linecap="round" stroke-linejoin="round"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>

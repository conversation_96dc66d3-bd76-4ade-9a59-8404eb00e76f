<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Airplay</title>
    <g id="超级编导-素材库、个人设置、周报日报" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="个人设置-关于蚁小二" transform="translate(-228.000000, -190.000000)">
            <g id="编组-4" transform="translate(216.000000, 90.000000)">
                <g id="编组-2备份-2" transform="translate(0.000000, 88.000000)">
                    <g id="Airplay" transform="translate(12.000000, 12.000000)">
                        <rect id="矩形" x="0" y="0" width="16" height="16"></rect>
                        <path d="M2,11.3333333 L2,4.66666667 C2,3.2 3.2,2 4.66666667,2 L11.3333333,2 C12.8,2 14,3.2 14,4.66666667 L14,11.3333333 C14,12.8 12.8,14 11.3333333,14 L4.66666667,14 C3.2,14 2,12.8 2,11.3333333 Z" id="路径" stroke="currentColor" stroke-width="1.33333333" stroke-linecap="round" stroke-linejoin="round"></path>
                        <line x1="4.66666667" y1="11" x2="7.33333333" y2="11" id="路径" stroke="currentColor" stroke-width="1.33333333" stroke-linecap="round" stroke-linejoin="round"></line>
                        <line x1="4.66666667" y1="5" x2="8.66666667" y2="5" id="路径" stroke="currentColor" stroke-width="1.33333333" stroke-linecap="round" stroke-linejoin="round"></line>
                        <line x1="4.66666667" y1="8" x2="11.3333333" y2="8" id="路径" stroke="currentColor" stroke-width="1.33333333" stroke-linecap="round" stroke-linejoin="round"></line>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>

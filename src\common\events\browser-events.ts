export const browserChannel = {
  openTab: 'openTab',
  newOpenTab: 'newOpenTab',
  activeTab: 'activeTab',
  closeTab: 'closeTab',
  restoreContext: 'restoreContext',
  createNewContext: 'createNewContext',
  createAccountContext: 'createAccountContext',
  updateAccountContext: 'updateAccountContext',
  closeAllTab: 'closeAllTab',
  openAccountTab: 'openAccountTab',
  openAccountTabInNewWindow: 'openAccountTabInNewWindow',
  openNewAccountContext: 'openNewAccountContext',
  isContextCreated: 'isContextCreated',
  anyWebSpaceTabOpened: 'anyWebSpaceTabOpened',
  anyAccountSpaceTabOpened: 'anyAccountSpaceTabOpened',
  getOpenedUrls: 'getOpenedUrls',
  syncFavorites: 'syncFavorites',
  contextUpdated: 'contextUpdated',
  closeOtherTab: 'closeOtherTab',
  closeRightTab: 'closeRightTab',
  saveFavorite: 'saveFavorite',
  removeFavorite: 'removeFavorite',
  cleanupSessionDirectory: 'cleanupSessionDirectory',
  contextFaviconUpdated: 'contextFaviconUpdated',
  hasSpaceIcon: 'hasSpaceIcon',
  getSpaceIcon: 'getSpaceIcon',
  updateSpaceIcon: 'updateSpaceIcon',
  spaceIconUpdated: 'spaceIconUpdated',
  setHeaderViewTop: 'setHeaderViewTop',
  setTabViewTop: 'setTabViewTop',
  tabViewTopped: 'tabViewTopped',
  headerUpdated: 'headerUpdated',
  headerInitialed: 'HeaderInitialed',
  resetHeaderHeight: 'changeHeaderHeight',
  goBack: 'goBack',
  goForward: 'goForward',
  refresh: 'refresh',
  authorize: 'authorize',
  isContextOpened: 'isContextOpened',
  closeBrowser: 'closeBrowser',
  getAccountSession: 'getAccountSession',
  getWebSession: 'getWebSession',
  checkCloseConfirm: 'checkCloseConfirm',
  confirmClose: 'confirmClose',
}

export const browserAuthorizeChannel = {
  accountAuthorizing: 'accountAuthorizing',
  accountAuthorizeSuccess: 'accountAuthorizeSuccess',
  accountAuthorizeError: 'accountAuthorizeError',
  accountSessionFailed: 'accountSessionFailed',
  accountIdentityVerified: 'accountIdentityVerified',
  webSpaceAuthorizeSuccess: 'webSpaceAuthorizeSuccess',
}

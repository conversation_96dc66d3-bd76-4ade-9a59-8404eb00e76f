import { API } from 'src/preload/API'

// 模拟Electron IPC通信的Web适配器
class WebAPIAdapter {
  env = import.meta.env

  // 事件监听器存储
  private listeners: Record<string, Array<(...args: unknown[]) => void>> = {}

  getPreloadPath = (relatedPath: string) => {
    return new URL(relatedPath, import.meta.url).pathname
  }

  createDownloader = () => {
    return null
  }

  // 模拟ipcRenderer.on
  on = (event: string, listener: () => unknown) => {
    if (!this.listeners[event]) {
      this.listeners[event] = []
    }
    this.listeners[event].push(listener)
    return () => this.off(event, listener)
  }

  // 模拟ipcRenderer.off
  off = (event: string, listener: () => unknown) => {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter((l) => l !== listener)
    }
  }

  // 模拟ipcRenderer.offAll
  offAll = (event: string) => {
    this.listeners[event] = []
  }

  // 模拟ipcRenderer.send
  send = (channel: string, ...args: unknown[]) => {
    console.log(`Web环境: 发送消息到 ${channel}`, args)
    // 在Web环境中，可以使用localStorage、IndexedDB或API调用替代
  }

  // 其他需要模拟的Electron API
  invoke = (channel: string, ...args: unknown[]) => {
    console.log(`Web环境: 调用 ${channel}`, args)
    // 在Web环境中，可以使用fetch、IndexedDB或API调用替代
    return Promise.resolve(null)
  }

  sendSync = (channel: string, ...args: unknown[]) => {
    console.log(`Web环境: 同步发送消息到 ${channel}`, args)
    // 在Web环境中，可以使用localStorage、IndexedDB或API调用替代
    return null
  }
}

// 导出Web适配器实例
export const webAPI = new WebAPIAdapter()

// 检测环境并导出适当的API

if (typeof window !== 'undefined' && !window.api) {
  window.api = webAPI as unknown as API
}
